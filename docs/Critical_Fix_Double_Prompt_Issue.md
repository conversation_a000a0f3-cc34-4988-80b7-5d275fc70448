# 关键修复：解决双重Prompt问题

## 问题发现

在重新设计TranslatorManager的过程中，发现了一个严重的架构不兼容问题：

### 原始问题

当使用`translateWithPrompt`和`filterComments`方法时，会出现**双重prompt问题**：

```coffeescript
# 在_translateWithCustomPrompt方法中
fullPrompt = "#{customPrompt} #{fromLang} to #{toLang}: #{content}"
result = await translator.translate(fullPrompt, fromLang, toLang)

# 但LLM翻译器的translate方法内部又会添加自己的prompt：
# "#{@prompt} #{fromLang} to #{toLang}: #{fullPrompt}"
```

**最终发送给LLM API的内容**：
```
"You are a real estate expert... English to Chinese: Please translate the following UI text to Chinese English to Chinese: Save changes"
```

这导致：
1. 重复的语言指令（English to Chinese出现两次）
2. 混乱的prompt结构
3. 翻译质量下降
4. 不必要的token消耗

## 解决方案

### 1. 架构分析

发现翻译器分为两类：

**A. LLM翻译器（继承自AITranslator）**：
- DeepSeekTranslator, OpenAITranslator, GeminiTranslator, ClaudeTranslator, RMTranslator
- 构造函数接收prompt参数
- translate方法内部使用固定的@prompt

**B. 传统翻译器（继承自Translator）**：
- AzureTranslator, DeepLTranslator
- 不使用prompt，直接翻译内容

### 2. 修复策略

为所有LLM翻译器添加`translateWithCustomPrompt`方法，支持动态prompt覆盖：

#### A. 修改AITranslator基类
```coffeescript
class AITranslator extends Translator
  # 添加抽象方法
  translateWithCustomPrompt: (content, customPrompt, fromLang, toLang) ->
    throw new Error('translateWithCustomPrompt method must be implemented by subclass')
```

#### B. 为每个LLM翻译器实现translateWithCustomPrompt
```coffeescript
# 示例：OpenAITranslator
translateWithCustomPrompt: (content, customPrompt, fromLang = 'English', toLang = 'Chinese') ->
  data = {
    model: @model
    messages: [
      { role: 'user', content: "#{customPrompt} #{fromLang} to #{toLang}: #{content}" }
    ]
    stream: false
  }
  @_executeTranslation(data)
```

#### C. 修改TranslatorManager的_translateWithCustomPrompt方法
```coffeescript
# 检查翻译器类型并相应处理
if translator.translateWithCustomPrompt?
  # LLM翻译器支持自定义prompt
  result = await translator.translateWithCustomPrompt(content, customPrompt, fromLang, toLang)
else
  # 传统翻译器（如Azure, DeepL）不支持prompt，直接翻译内容
  result = await translator.translate(content, fromLang, toLang)
```

## 修复的文件

### 1. 核心文件
- `src/lib/translator/AITranslator.coffee` - 添加抽象方法
- `src/lib/translator/translatorManager.coffee` - 修改调用逻辑

### 2. LLM翻译器文件
- `src/lib/translator/deepseekTranslator.coffee` - 添加translateWithCustomPrompt
- `src/lib/translator/openAITranslator.coffee` - 添加translateWithCustomPrompt
- `src/lib/translator/geminiTranslator.coffee` - 添加translateWithCustomPrompt
- `src/lib/translator/claudeTranslator.coffee` - 添加translateWithCustomPrompt
- `src/lib/translator/rmTranslator.coffee` - 添加translateWithCustomPrompt

### 3. 测试和文档文件
- `src/lib/translator/integration_test.coffee` - 更新测试
- `src/lib/translator/translatorManager_test_example.coffee` - 更新示例
- `docs/Critical_Fix_Double_Prompt_Issue.md` - 本文档

## 修复效果

### 修复前
```
发送给API: "You are a real estate expert... English to Chinese: Please translate UI text English to Chinese: Save changes"
```

### 修复后
```
发送给API: "Please translate UI text English to Chinese: Save changes"
```

## 向后兼容性

✅ **完全保持向后兼容**：
- 现有的`translate`方法保持不变
- 传统翻译器（Azure, DeepL）继续正常工作
- 所有现有代码无需修改

## 代码质量改进

在修复过程中，还进行了以下代码质量改进：

1. **代码风格统一**：修复了所有CoffeeScript代码风格问题
2. **代码重构**：为LLM翻译器添加了`_executeTranslation`通用方法
3. **长行处理**：将过长的代码行进行了合理分割
4. **引号统一**：统一使用单引号而非双引号

## 测试验证

### 1. 功能测试
- ✅ translateWithPrompt方法正常工作
- ✅ filterComments方法正常工作
- ✅ 传统translate方法保持兼容
- ✅ 不同类型翻译器都能正确处理

### 2. Prompt测试
- ✅ 自定义prompt正确传递给LLM
- ✅ 不再出现双重prompt问题
- ✅ 语言指令不再重复

### 3. 错误处理测试
- ✅ 翻译器不可用时正确降级
- ✅ API错误时正确抛出异常
- ✅ 参数验证正常工作

## 性能影响

### 正面影响
- **Token消耗减少**：消除了重复的prompt内容
- **翻译质量提升**：清晰的prompt结构
- **响应速度提升**：减少了不必要的内容传输

### 无负面影响
- 不增加额外的API调用
- 不影响现有功能性能
- 不增加内存消耗

## 总结

这次修复解决了一个关键的架构问题，确保了：

1. **功能正确性**：消除了双重prompt问题
2. **架构清晰性**：明确区分了不同类型的翻译器
3. **向后兼容性**：保持了所有现有功能
4. **代码质量**：提升了整体代码质量
5. **可维护性**：为未来扩展奠定了良好基础

这个修复是TranslatorManager重新设计过程中的一个重要里程碑，确保了新功能的正确性和可靠性。
