# TranslatorManager LLM功能扩展文档

## 概述

TranslatorManager已扩展支持基于Prompt模板的智能翻译功能，包括：

1. **基于场景的智能翻译** - 根据不同应用场景自动选择合适的Prompt模板
2. **LLM评论过滤功能** - 使用AI进行内容审核和过滤
3. **i18n过滤规则集成** - 自动过滤不需要翻译的内容
4. **向后兼容性** - 保持现有translate方法的完整功能

## 新增功能

### 1. translateWithPrompt - 基于场景的智能翻译

#### 方法签名
```coffeescript
translateWithPrompt(content, scenario, targetLang, options)
```

#### 参数说明
- `content` (String): 要翻译的内容
- `scenario` (String): 应用场景，支持：
  - `'ui_translation'` - UI界面翻译
  - `'forum_translation'` - 论坛内容翻译
  - `'comment_filter'` - 评论过滤
  - `'general'` - 通用翻译
- `targetLang` (String): 目标语言键，默认 'zh-cn'
- `options` (Object): 可选参数
  - `fromLang` (String): 源语言键，默认 'en'
  - `translatorList` (Array): 翻译服务列表
  - `tags` (Array): 模板标签过滤
  - `variables` (Object): 额外的模板变量

#### 返回值
```coffeescript
{
  success: Boolean,      # 是否成功
  result?: String,       # 翻译结果
  service?: String,      # 使用的服务名称
  template?: String,     # 使用的模板ID
  scenario?: String,     # 应用场景
  error?: String,        # 错误信息
  filtered?: Boolean,    # 是否被过滤
  fallback?: Boolean     # 是否降级到传统翻译
}
```

#### 使用示例
```coffeescript
# UI翻译示例
result = await translatorManager.translateWithPrompt(
  'Save changes',
  'ui_translation',
  'zh-cn',
  { tags: ['ui', 'button'] }
)

# Forum翻译示例
result = await translatorManager.translateWithPrompt(
  'This property has excellent location.',
  'forum_translation',
  'kr',
  { variables: { context: 'property description' } }
)
```

### 2. filterComments - 评论过滤功能

#### 方法签名
```coffeescript
filterComments(content, options)
```

#### 参数说明
- `content` (String): 要过滤的评论内容
- `options` (Object): 可选参数
  - `translatorList` (Array): 翻译服务列表
  - `language` (String): 内容语言，默认 'en'

#### 返回值
```coffeescript
{
  success: Boolean,      # 是否成功
  passed: Boolean,       # 是否通过过滤
  reason?: String,       # 过滤原因
  service?: String,      # 使用的服务名称
  template?: String,     # 使用的模板ID
  error?: String         # 错误信息
}
```

#### 使用示例
```coffeescript
# 评论过滤示例
result = await translatorManager.filterComments(
  'This is a great property!',
  { language: 'en' }
)

if result.success and result.passed
  console.log '评论通过审核'
else
  console.log '评论被拒绝:', result.reason
```

## 集成的过滤规则

新功能自动集成了i18n的过滤规则，以下内容会被自动过滤：

- undefined/null值
- 中文字符
- 纯数字
- 错误信息
- 空内容或特殊前缀
- 邮政编码
- URL编码
- HTTP链接
- 过长的单词
- 不含英文字母的字符串
- 超长字符串（>100字符）
- 邮箱地址
- HTML/XML标签

## Prompt模板系统集成

### 模板选择逻辑

1. **场景匹配**: 根据scenario参数选择对应场景的模板
2. **标签过滤**: 支持通过tags参数进一步筛选模板
3. **内容特征**: 根据内容长度等特征智能选择最合适的模板
4. **版本优先**: 自动选择最新版本的活跃模板

### 变量替换

系统自动注入以下变量：
- `text`: 要翻译的内容
- `target_language`: 目标语言名称
- 用户自定义变量（通过options.variables传入）

## 错误处理和容错机制

### 多级容错
1. **模板选择失败**: 自动降级到传统翻译方法
2. **服务失败**: 按优先级尝试下一个翻译服务
3. **过滤失败**: 评论过滤失败时默认拒绝通过

### 详细日志
- 模板选择过程
- 翻译服务使用情况
- 错误详情和堆栈信息
- 性能统计信息

## 性能考虑

### 资源管理
- 继承现有的maxUsage限制机制
- 等待队列避免服务过载
- 智能服务选择减少延迟

### 缓存优化
- 模板查询结果可缓存
- 过滤规则预编译
- 语言映射表优化

## 向后兼容性

所有现有的translate方法调用保持完全兼容：

```coffeescript
# 现有代码无需修改
[result, service] = await translatorManager.translate(
  'Hello world',
  ['gemini', 'openAI'],
  'en',
  'zh-cn'
)
```

## 配置要求

无需额外配置，新功能会自动检测Prompts模型的可用性：

- 如果Prompts模型可用，启用智能翻译功能
- 如果不可用，自动降级到传统翻译方法
- 所有错误都有适当的日志记录

## 最佳实践

1. **场景选择**: 根据实际应用场景选择合适的scenario
2. **标签使用**: 使用标签进一步细化模板选择
3. **错误处理**: 始终检查返回结果的success字段
4. **性能监控**: 关注日志中的服务使用情况和响应时间
5. **模板管理**: 定期更新和优化Prompt模板以提高翻译质量

## 故障排除

### 常见问题

1. **模板未找到**: 检查scenario参数是否正确，确保数据库中有对应的活跃模板
2. **翻译失败**: 检查翻译服务配置和API密钥
3. **过滤异常**: 检查内容是否触发了i18n过滤规则
4. **性能问题**: 检查服务使用限制和等待队列状态

### 调试建议

1. 启用详细日志记录
2. 检查Prompts模型的可用性
3. 验证翻译服务配置
4. 测试不同场景和内容类型
