debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'


class OpenAITranslator extends AITranslator
  constructor: (apiKey, endpoint, model, prompt, orgID, projectID,maxUsage) ->
    super(apiKey,endpoint,model,prompt,maxUsage)
    @orgID=orgID
    @projectID=projectID
    
  translate: (message, fromLang = 'English', toLang = 'Chinese') ->
    data = {
      model: @model
      messages: [
        { role: 'user', content: "#{@prompt} #{fromLang} to #{toLang}: #{message}" }
      ]
      stream: false
    }
    @_executeTranslation(data)

  ###
  使用自定义prompt进行翻译
  ###
  translateWithCustomPrompt: (content, customPrompt, fromLang = 'English', toLang = 'Chinese') ->
    data = {
      model: @model
      messages: [
        { role: 'user', content: "#{customPrompt} #{fromLang} to #{toLang}: #{content}" }
      ]
      stream: false
    }
    @_executeTranslation(data)

  ###
  执行翻译请求的通用方法
  ###
  _executeTranslation: (data) ->
    try
      @use()
      response = await fetch(@endpoint,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
          'Authorization': "Bearer #{@apiKey}"
          'OpenAI-Organization': @orgID
          'OpenAI-Project': @projectID
        },
        body: JSON.stringify(data)
      )
      ret = await response.json()
      debug.debug ret
      if response.ok
        translatedContent = ret.choices[0].message.content
        translatedContentDeleteN = translatedContent.replace(/\n+$/, "")
        return translatedContentDeleteN
      else
        throw new Error(ret.message or ret)
    catch error
      debug.error "OpenAI Translation API Error:", {
        error: {
          name: error.name
          message: error.message
          stack: error.stack
          details: error  # 完整错误对象
        }
        response: ret  # API 返回的原始响应
      }
      throw error
    finally
      @release()

module.exports = OpenAITranslator