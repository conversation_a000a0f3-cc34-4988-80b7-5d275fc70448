###
TranslatorManager 集成测试

这个文件用于测试TranslatorManager的新功能是否正确集成到现有系统中
包括外部Prompt的处理、i18n过滤规则的应用等

运行方法：
node -r coffeescript/register src/lib/translator/integration_test.coffee
###

# 模拟必要的依赖

mocki18nHelper = {
  i18nRegexArray: [
    { regex: /^\d+$/, message: 'Pure numbers not allowed' }
    { regex: /^$/, message: 'Empty content not allowed' }
    { regex: /undefined|null/i, message: 'undefined or null not allowed' }
  ]
}

# 模拟翻译器
mockTranslator = {
  isAvailable: -> true
  translate: (message, fromLang, toLang) ->
    # 简单的模拟翻译
    if message.includes('Please translate the following UI text to Chinese')
      return '保存更改'  # 模拟UI翻译结果
    else if message.includes('Please check if this content is appropriate')
      return 'PASS'  # 模拟过滤通过
    else
      return 'Translated: ' + message
  usageCount: 0
  maxUsage: 10
}

# 创建测试用的TranslatorManager类
class TestTranslatorManager
  constructor: ->
    @translators = { 'openAI': mockTranslator }
    @waitingQueue = []
    @i18nHelper = mocki18nHelper

  # 复制主要的方法（简化版本用于测试）
  _shouldFilterContent: (content) ->
    return true unless content and typeof content is 'string'
    return true if content.trim().length is 0
    
    if @i18nHelper?.i18nRegexArray
      for regexObj in @i18nHelper.i18nRegexArray
        if regexObj.regex.test(content)
          console.log "Content filtered: #{regexObj.message}"
          return true
    return false

  getAvailableTranslator: (translatorList) ->
    for service in translatorList
      if @translators[service]?.isAvailable()
        return service
    return null

  _translateWithCustomPrompt: (content, prompt, fromLang, toLang, translatorList) ->
    translator = @translators['openAI']
    fullPrompt = "#{prompt} #{content}"
    result = await translator.translate(fullPrompt, fromLang, toLang)
    return [result, 'openAI']

  translateWithPrompt: (content, prompt, targetLang = 'zh-cn', options = {}) ->
    try
      # 参数验证
      unless content and typeof content is 'string'
        return {success: false, error: 'Invalid content parameter'}

      unless prompt and typeof prompt is 'string'
        return {success: false, error: 'Invalid prompt parameter'}

      # 检查过滤
      if @_shouldFilterContent(content)
        return {success: false, error: 'Content filtered', filtered: true}

      # 使用外部Prompt进行翻译
      [result, service] = await @_translateWithCustomPrompt(
        content,
        prompt,
        'en',
        targetLang,
        ['openAI']
      )

      return {
        success: true
        result: result
        service: service
        prompt: prompt
      }

    catch error
      return {success: false, error: error.message}

  filterComments: (content, filterPrompt, options = {}) ->
    try
      unless content and typeof content is 'string'
        return {success: false, error: 'Invalid content parameter'}

      unless filterPrompt and typeof filterPrompt is 'string'
        return {success: false, error: 'Invalid filterPrompt parameter'}

      if content.trim().length is 0
        return {success: true, passed: false, reason: 'Empty content'}

      # 使用外部过滤Prompt
      [result, service] = await @_translateWithCustomPrompt(
        content,
        filterPrompt,
        'en',
        'en',
        ['openAI']
      )

      passed = result.toLowerCase().includes('pass')
      reason = if passed then 'Content approved' else 'Content rejected'

      return {
        success: true
        passed: passed
        reason: reason
        service: service
        filterPrompt: filterPrompt
      }

    catch error
      return {success: false, passed: false, error: error.message}

# 运行测试
runIntegrationTests = ->
  console.log '=== TranslatorManager 集成测试开始 ===\n'
  
  manager = new TestTranslatorManager()
  
  # 测试1: 正常UI翻译
  console.log '测试1: UI翻译功能'
  uiPrompt = 'Please translate the following UI text to Chinese: '
  result = await manager.translateWithPrompt('Save changes', uiPrompt, 'zh-cn')
  console.log '结果:', result
  console.log '预期: success=true, 有翻译结果\n'

  # 测试2: 内容过滤
  console.log '测试2: 内容过滤功能'
  result = await manager.translateWithPrompt('12345', uiPrompt, 'zh-cn')
  console.log '结果:', result
  console.log '预期: success=false, filtered=true\n'

  # 测试3: 评论过滤 - 通过
  console.log '测试3: 评论过滤 - 正常内容'
  filterPrompt = 'Please check if this content is appropriate. Reply PASS or REJECT: '
  result = await manager.filterComments('This is a good property', filterPrompt)
  console.log '结果:', result
  console.log '预期: success=true, passed=true\n'

  # 测试4: 评论过滤 - 空内容
  console.log '测试4: 评论过滤 - 空内容'
  result = await manager.filterComments('', filterPrompt)
  console.log '结果:', result
  console.log '预期: success=true, passed=false\n'

  # 测试5: 无效参数
  console.log '测试5: 无效参数'
  result = await manager.translateWithPrompt('Hello', '', 'zh-cn')
  console.log '结果:', result
  console.log '预期: success=false, error包含"Invalid prompt parameter"\n'
  
  console.log '=== 集成测试完成 ==='

# 运行测试
if require.main is module
  runIntegrationTests().catch (error) ->
    console.error '测试失败:', error
    process.exit(1)
