###
TranslatorManager 集成测试

这个文件用于测试TranslatorManager的新功能是否正确集成到现有系统中
包括Prompts模型的集成、i18n过滤规则的应用等

运行方法：
node -r coffeescript/register src/lib/translator/integration_test.coffee
###

# 模拟必要的依赖
mockPromptsModel = {
  findActiveByScenario: (scenario, options) ->
    # 模拟返回一个UI翻译模板
    if scenario is 'ui_translation'
      return [{
        _id: 'ui_menu_gpt4_v1'
        nm: 'UI菜单翻译模板'
        scenario: 'ui_translation'
        tags: ['ui', 'menu']
        m_cfg: { m_nm: 'gpt', params: { temperature: 0.3 } }
        tpl: {
          main: '请将以下英文UI术语翻译为{target_language}: {text}'
          sys: '你是一个专业的UI界面翻译专家'
        }
        vars: [
          { nm: 'text', tp: 'string', req: true }
          { nm: 'target_language', tp: 'string', req: true }
        ]
      }]
    else if scenario is 'comment_filter'
      return [{
        _id: 'comment_filter_v1'
        nm: '评论过滤模板'
        scenario: 'comment_filter'
        tags: ['filter', 'comment']
        m_cfg: { m_nm: 'gpt' }
        tpl: {
          main: '请判断以下内容是否适合发布: {content}。回答PASS或REJECT: reason'
        }
        vars: [
          { nm: 'content', tp: 'string', req: true }
        ]
      }]
    return []

  replaceVariables: (template, variables) ->
    # 简单的变量替换模拟
    content = template.tpl.main
    for varName, varValue of variables
      regex = new RegExp("\\{#{varName}\\}", 'g')
      content = content.replace(regex, String(varValue))
    
    return {
      success: true
      content: {
        main: content
        sys: template.tpl.sys
        model: template.m_cfg?.m_nm
        params: template.m_cfg?.params or {}
      }
    }
}

mocki18nHelper = {
  i18nRegexArray: [
    { regex: /^\d+$/, message: 'Pure numbers not allowed' }
    { regex: /^$/, message: 'Empty content not allowed' }
    { regex: /undefined|null/i, message: 'undefined or null not allowed' }
  ]
}

# 模拟翻译器
mockTranslator = {
  isAvailable: -> true
  translate: (message, fromLang, toLang) ->
    # 简单的模拟翻译
    if message.includes('请将以下英文UI术语翻译为')
      return '保存更改'  # 模拟UI翻译结果
    else if message.includes('请判断以下内容是否适合发布')
      return 'PASS'  # 模拟过滤通过
    else
      return 'Translated: ' + message
  usageCount: 0
  maxUsage: 10
}

# 创建测试用的TranslatorManager类
class TestTranslatorManager
  constructor: ->
    @translators = { 'openAI': mockTranslator }
    @waitingQueue = []
    @promptsModel = mockPromptsModel
    @i18nHelper = mocki18nHelper

  # 复制主要的方法（简化版本用于测试）
  _shouldFilterContent: (content) ->
    return true unless content and typeof content is 'string'
    return true if content.trim().length is 0
    
    if @i18nHelper?.i18nRegexArray
      for regexObj in @i18nHelper.i18nRegexArray
        if regexObj.regex.test(content)
          console.log "Content filtered: #{regexObj.message}"
          return true
    return false

  _selectPromptTemplate: (scenario, content, options = {}) ->
    templates = await @promptsModel.findActiveByScenario(scenario, options)
    return if templates.length > 0 then templates[0] else null

  getAvailableTranslator: (translatorList) ->
    for service in translatorList
      if @translators[service]?.isAvailable()
        return service
    return null

  translateWithPrompt: (content, scenario, targetLang = 'zh-cn', options = {}) ->
    try
      # 参数验证
      unless content and typeof content is 'string'
        return {success: false, error: 'Invalid content parameter'}
      
      # 检查过滤
      if @_shouldFilterContent(content)
        return {success: false, error: 'Content filtered', filtered: true}
      
      # 选择模板
      template = await @_selectPromptTemplate(scenario, content, options)
      if not template
        return {success: false, error: 'No template found'}
      
      # 替换变量
      variables = {
        text: content
        target_language: 'Chinese'
      }
      promptResult = @promptsModel.replaceVariables(template, variables)
      
      # 模拟翻译
      translator = @translators['openAI']
      result = await translator.translate(promptResult.content.main, 'English', 'Chinese')
      
      return {
        success: true
        result: result
        service: 'openAI'
        template: template._id
        scenario: scenario
      }
      
    catch error
      return {success: false, error: error.message}

  filterComments: (content, options = {}) ->
    try
      unless content and typeof content is 'string'
        return {success: false, error: 'Invalid content parameter'}
      
      if content.trim().length is 0
        return {success: true, passed: false, reason: 'Empty content'}
      
      template = await @_selectPromptTemplate('comment_filter', content, options)
      if not template
        return {success: true, passed: true, reason: 'No filter template, default pass'}
      
      variables = { content: content }
      promptResult = @promptsModel.replaceVariables(template, variables)
      
      translator = @translators['openAI']
      result = await translator.translate(promptResult.content.main, 'English', 'English')
      
      passed = result.toLowerCase().includes('pass')
      reason = if passed then 'Content approved' else 'Content rejected'
      
      return {
        success: true
        passed: passed
        reason: reason
        service: 'openAI'
        template: template._id
      }
      
    catch error
      return {success: false, passed: false, error: error.message}

# 运行测试
runIntegrationTests = ->
  console.log '=== TranslatorManager 集成测试开始 ===\n'
  
  manager = new TestTranslatorManager()
  
  # 测试1: 正常UI翻译
  console.log '测试1: UI翻译功能'
  result = await manager.translateWithPrompt('Save changes', 'ui_translation', 'zh-cn')
  console.log '结果:', result
  console.log '预期: success=true, 有翻译结果\n'
  
  # 测试2: 内容过滤
  console.log '测试2: 内容过滤功能'
  result = await manager.translateWithPrompt('12345', 'ui_translation', 'zh-cn')
  console.log '结果:', result
  console.log '预期: success=false, filtered=true\n'
  
  # 测试3: 评论过滤 - 通过
  console.log '测试3: 评论过滤 - 正常内容'
  result = await manager.filterComments('This is a good property')
  console.log '结果:', result
  console.log '预期: success=true, passed=true\n'
  
  # 测试4: 评论过滤 - 空内容
  console.log '测试4: 评论过滤 - 空内容'
  result = await manager.filterComments('')
  console.log '结果:', result
  console.log '预期: success=true, passed=false\n'
  
  # 测试5: 无模板场景
  console.log '测试5: 无模板场景'
  result = await manager.translateWithPrompt('Hello', 'unknown_scenario', 'zh-cn')
  console.log '结果:', result
  console.log '预期: success=false, error包含"No template found"\n'
  
  console.log '=== 集成测试完成 ==='

# 运行测试
if require.main is module
  runIntegrationTests().catch (error) ->
    console.error '测试失败:', error
    process.exit(1)
