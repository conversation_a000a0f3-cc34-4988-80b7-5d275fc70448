###
TranslatorManager 新功能测试示例

这个文件展示了如何使用TranslatorManager的新功能：
1. translateWithPrompt - 基于外部Prompt的翻译
2. filterComments - 基于外部Prompt的评论过滤功能

注意：这是一个示例文件，实际使用时需要根据项目结构调整路径和配置
###

# 引入必要的模块
translatorManagerLib = require './translatorManager'

# 示例配置（实际使用时从配置文件读取）
sampleConfig = {
  openAI: {
    key: 'your-openai-key'
    endpoint: 'https://api.openai.com/v1/chat/completions'
    maxUsage: 10
  }
  gemini: {
    key: 'your-gemini-key'
    maxUsage: 10
  }
  claude: {
    key: 'your-claude-key'
    endpoint: 'https://api.anthropic.com/v1/messages'
    maxUsage: 10
  }
}

# 创建翻译管理器实例
translatorManager = translatorManagerLib.createTranslatorManager(sampleConfig)

###
测试基于外部Prompt的翻译功能
###
testTranslateWithPrompt = ->
  console.log '=== 测试 translateWithPrompt 功能 ==='

  try
    # 测试UI翻译Prompt
    console.log '\n1. 测试UI翻译Prompt:'
    uiPrompt = 'Please translate the following UI text to Chinese, ' +
      'keep it concise and user-friendly: '
    result = await translatorManager.translateWithPrompt(
      'Save changes',
      uiPrompt,
      'zh-cn'
    )
    console.log 'UI翻译结果:', result

    # 测试房产描述翻译Prompt
    console.log '\n2. 测试房产描述翻译Prompt:'
    propertyPrompt = 'Please translate the following real estate description to Chinese, ' +
      'maintain professional tone and do not translate place names: '
    result = await translatorManager.translateWithPrompt(
      'This is a great property with excellent location and amenities.',
      propertyPrompt,
      'zh-cn'
    )
    console.log '房产翻译结果:', result

    # 测试韩语翻译Prompt
    console.log '\n3. 测试韩语翻译Prompt:'
    koreanPrompt = 'Please translate the following text to Korean: '
    result = await translatorManager.translateWithPrompt(
      'Welcome to our real estate platform',
      koreanPrompt,
      'kr'
    )
    console.log '韩语翻译结果:', result

    # 测试过滤内容
    console.log '\n4. 测试内容过滤:'
    result = await translatorManager.translateWithPrompt(
      '123456',  # 纯数字，应该被过滤
      uiPrompt,
      'zh-cn'
    )
    console.log '过滤测试结果:', result

  catch error
    console.error '翻译测试出错:', error

###
测试基于外部Prompt的评论过滤功能
###
testFilterComments = ->
  console.log '\n=== 测试 filterComments 功能 ==='

  try
    # 定义过滤Prompt
    filterPrompt = 'Please check if the following content is appropriate for publication. ' +
      'Reply with PASS if appropriate, or REJECT: reason if not appropriate. Content: '

    # 测试正常评论
    console.log '\n1. 测试正常评论:'
    result = await translatorManager.filterComments(
      'This is a very nice property with great location.',
      filterPrompt,
      { language: 'en' }
    )
    console.log '正常评论过滤结果:', result

    # 测试空内容
    console.log '\n2. 测试空内容:'
    result = await translatorManager.filterComments(
      '',
      filterPrompt,
      { language: 'en' }
    )
    console.log '空内容过滤结果:', result

    # 测试可能不当的内容
    console.log '\n3. 测试可能不当的内容:'
    result = await translatorManager.filterComments(
      'This property is terrible and overpriced!',
      filterPrompt,
      { language: 'en' }
    )
    console.log '不当内容过滤结果:', result

  catch error
    console.error '评论过滤测试出错:', error

###
测试传统翻译功能（确保向后兼容）
###
testTraditionalTranslate = ->
  console.log '\n=== 测试传统翻译功能（向后兼容） ==='
  
  try
    [result, service] = await translatorManager.translate(
      'Hello world',
      ['gemini', 'openAI'],
      'en',
      'zh-cn'
    )
    console.log "传统翻译结果: #{result}, 使用服务: #{service}"
    
  catch error
    console.error '传统翻译测试出错:', error

###
运行所有测试
###
runAllTests = ->
  console.log 'TranslatorManager 新功能测试开始...\n'
  
  await testTranslateWithPrompt()
  await testFilterComments()
  await testTraditionalTranslate()
  
  console.log '\n测试完成!'

# 导出测试函数（如果需要在其他地方调用）
module.exports = {
  testTranslateWithPrompt
  testFilterComments
  testTraditionalTranslate
  runAllTests
}

# 如果直接运行此文件，执行所有测试
if require.main is module
  runAllTests().catch (error) ->
    console.error '测试执行失败:', error
    process.exit(1)
