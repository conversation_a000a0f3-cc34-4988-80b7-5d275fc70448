###
TranslatorManager 新功能测试示例

这个文件展示了如何使用TranslatorManager的新功能：
1. translateWithPrompt - 基于场景的智能翻译
2. filterComments - 评论过滤功能

注意：这是一个示例文件，实际使用时需要根据项目结构调整路径和配置
###

# 引入必要的模块
translatorManagerLib = require './translatorManager'

# 示例配置（实际使用时从配置文件读取）
sampleConfig = {
  openAI: {
    key: 'your-openai-key'
    endpoint: 'https://api.openai.com/v1/chat/completions'
    maxUsage: 10
  }
  gemini: {
    key: 'your-gemini-key'
    maxUsage: 10
  }
  claude: {
    key: 'your-claude-key'
    endpoint: 'https://api.anthropic.com/v1/messages'
    maxUsage: 10
  }
}

# 创建翻译管理器实例
translatorManager = translatorManagerLib.createTranslatorManager(sampleConfig)

###
测试基于场景的智能翻译功能
###
testTranslateWithPrompt = ->
  console.log '=== 测试 translateWithPrompt 功能 ==='
  
  try
    # 测试UI翻译场景
    console.log '\n1. 测试UI翻译场景:'
    result = await translatorManager.translateWithPrompt(
      'Save changes',
      'ui_translation',
      'zh-cn',
      {
        tags: ['ui', 'button']
        variables: { context: 'button text' }
      }
    )
    console.log 'UI翻译结果:', result
    
    # 测试Forum翻译场景
    console.log '\n2. 测试Forum翻译场景:'
    result = await translatorManager.translateWithPrompt(
      'This is a great property with excellent location and amenities.',
      'forum_translation',
      'zh-cn',
      {
        tags: ['forum', 'property']
      }
    )
    console.log 'Forum翻译结果:', result
    
    # 测试通用翻译场景
    console.log '\n3. 测试通用翻译场景:'
    result = await translatorManager.translateWithPrompt(
      'Welcome to our real estate platform',
      'general',
      'kr'
    )
    console.log '通用翻译结果:', result
    
    # 测试过滤内容
    console.log '\n4. 测试内容过滤:'
    result = await translatorManager.translateWithPrompt(
      '123456',  # 纯数字，应该被过滤
      'ui_translation',
      'zh-cn'
    )
    console.log '过滤测试结果:', result
    
  catch error
    console.error '翻译测试出错:', error

###
测试评论过滤功能
###
testFilterComments = ->
  console.log '\n=== 测试 filterComments 功能 ==='
  
  try
    # 测试正常评论
    console.log '\n1. 测试正常评论:'
    result = await translatorManager.filterComments(
      'This is a very nice property with great location.',
      { language: 'en' }
    )
    console.log '正常评论过滤结果:', result
    
    # 测试空内容
    console.log '\n2. 测试空内容:'
    result = await translatorManager.filterComments(
      '',
      { language: 'en' }
    )
    console.log '空内容过滤结果:', result
    
    # 测试长内容
    console.log '\n3. 测试长内容:'
    longContent = 'This is a very long comment. '.repeat(50)
    result = await translatorManager.filterComments(
      longContent,
      { language: 'en' }
    )
    console.log '长内容过滤结果:', result
    
  catch error
    console.error '评论过滤测试出错:', error

###
测试传统翻译功能（确保向后兼容）
###
testTraditionalTranslate = ->
  console.log '\n=== 测试传统翻译功能（向后兼容） ==='
  
  try
    [result, service] = await translatorManager.translate(
      'Hello world',
      ['gemini', 'openAI'],
      'en',
      'zh-cn'
    )
    console.log "传统翻译结果: #{result}, 使用服务: #{service}"
    
  catch error
    console.error '传统翻译测试出错:', error

###
运行所有测试
###
runAllTests = ->
  console.log 'TranslatorManager 新功能测试开始...\n'
  
  await testTranslateWithPrompt()
  await testFilterComments()
  await testTraditionalTranslate()
  
  console.log '\n测试完成!'

# 导出测试函数（如果需要在其他地方调用）
module.exports = {
  testTranslateWithPrompt
  testFilterComments
  testTraditionalTranslate
  runAllTests
}

# 如果直接运行此文件，执行所有测试
if require.main is module
  runAllTests().catch (error) ->
    console.error '测试执行失败:', error
    process.exit(1)
