Translator = require './translator'

class AITranslator extends Translator
  constructor: (apiKey, endpoint, model, prompt, maxUsage) ->
    super(apiKey, endpoint, maxUsage)
    @model = model
    @prompt = prompt

  ###
  使用自定义prompt进行翻译（子类需要实现）
  @param {String} content - 要翻译的内容
  @param {String} customPrompt - 自定义提示词
  @param {String} fromLang - 源语言
  @param {String} toLang - 目标语言
  @return {String} 翻译结果
  ###
  translateWithCustomPrompt: (content, customPrompt, fromLang, toLang) ->
    throw new Error('translateWithCustomPrompt method must be implemented by subclass')

module.exports = AITranslator