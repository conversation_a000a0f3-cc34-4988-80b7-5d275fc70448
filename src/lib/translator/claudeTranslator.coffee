debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'


class ClaudeTranslator extends AITranslator
  constructor: (apiKey,endpoint,model,prompt,maxUsage) ->
    super(apiKey,endpoint,model,prompt,maxUsage)
    
  translate: (message, fromLang = 'English', toLang = 'Chinese') ->
    data = {
      model: @model
      max_tokens: 1024
      messages: [
        { role: 'user', content: "#{@prompt} #{fromLang} to #{toLang}: #{message}" }
      ]
    }
    @_executeTranslation(data)

  ###
  使用自定义prompt进行翻译
  ###
  translateWithCustomPrompt: (content, customPrompt, fromLang = 'English', toLang = 'Chinese') ->
    data = {
      model: @model
      max_tokens: 1024
      messages: [
        { role: 'user', content: "#{customPrompt} #{fromLang} to #{toLang}: #{content}" }
      ]
    }
    @_executeTranslation(data)

  ###
  执行翻译请求的通用方法
  ###
  _executeTranslation: (data) ->
    try
      @use()
      response = await fetch(@endpoint,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
          'x-api-key': "#{@apiKey}"
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify(data)
      )
      ret = await response.json()
      debug.debug ret
      if response.ok
        translatedContent = ret.content[0].text
        translatedContentDeleteN = translatedContent.replace(/\n+$/, '')
        return translatedContentDeleteN
      else
        throw new Error(ret.message)
    catch error
      debug.error 'Claude Translation API Error:', error.message
      throw error
    finally
      @release()

module.exports = ClaudeTranslator