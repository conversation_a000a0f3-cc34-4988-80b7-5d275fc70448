###
TranslatorManager - 多服务翻译管理器

支持传统翻译和基于Prompt模板的智能翻译功能，包括：
- 多种翻译服务的统一管理和容错机制
- 基于场景的智能翻译（使用Prompt模板）
- LLM评论过滤功能
- i18n过滤规则集成
- 资源管理和等待队列机制

使用示例：

```coffeescript
# 创建翻译管理器
config = {
  openAI: { key: 'your-key', endpoint: 'https://api.openai.com/v1/chat/completions' }
  gemini: { key: 'your-key' }
}
translatorManager = require('./translatorManager').createTranslatorManager(config)

# 传统翻译
[result, service] = await translatorManager.translate('Hello world')

# 基于场景的智能翻译
result = await translatorManager.translateWithPrompt(
  'Save changes',
  'ui_translation',
  'zh-cn',
  { tags: ['ui', 'button'] }
)

# 评论过滤
filterResult = await translatorManager.filterComments(
  'This is a test comment',
  { language: 'en' }
)
```

<AUTHOR> Team
@version 1.0.0
@since 2025-06-30
###

debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AzureTranslator = require './azureTranslator'
DeepLTranslator = require './deepLTranslator'
DeepSeekTranslator = require './deepseekTranslator'
OpenAITranslator = require './openAITranslator'
GeminiTranslator = require './geminiTranslator'
ClaudeTranslator = require './claudeTranslator'
GrokTranslator = require './grokTranslator'
RMTranslator = require './rmTranslator'

# 引入Prompts模型和i18n过滤逻辑
try
  PromptsModel = require '../../model/prompts'
  i18nHelper = require '../i18n'
catch error
  debug.warn 'Failed to load Prompts model or i18n helper:', error.message
  PromptsModel = null
  i18nHelper = null

# 默认翻译提示词（保持向后兼容）
DEFAULT_PROMPT = 'You are a real estate expert. The following content is about ' +
  'Canada real estate listing. Please translate all of the following content, ' +
  'but do not translate place names. Only return the translated text, ' +
  'do not include any other text. Translate the following text from English to Chinese.'

# 模型配置常量
MODEL_DEEPSEEK = 'deepseek-chat'
MODEL_OPENAI = 'gpt-4o-mini'
MODEL_GEMINI = 'gemini-2.0-flash-lite' # 'gemini-1.5-flash'
MODEL_CLAUDE = 'claude-3-haiku-20240307'
MODEL_GROK = 'grok-2-latest'
MODEL_RM = 'gemma3:12b'
# grok can not be used in app now because of the request limit
TRANSLATORLIST = ['gemini', 'openAI', 'claude', 'azure']

LANGUAGE_OBJ = [
  {k: 'en', v:'English', AIValue: 'English', azureValue: 'en', deepLValue:'EN'},
  {k: 'zh-cn', v:'简体中文', AIValue: 'Chinese', azureValue: 'zh-Hans', deepLValue:'ZH'},
  {k: 'zh', v:'繁体中文', AIValue: 'Traditional Chinese', azureValue: 'zh-Hant', deepLValue:'ZH-HANT'},
  {k: 'kr', v:'한국어', AIValue: 'Korean', azureValue: 'ko', deepLValue:'KO'},
]

findLanguageValue = (k, service) ->
  result = LANGUAGE_OBJ.find (language) -> language.k is k
  if result?
    switch service
      when 'azure' then result.azureValue
      when 'deepl' then result.deepLValue
      when 'deepseek', 'openAI', 'gemini', 'claude', 'grok', 'rm' then result.AIValue
      else throw new Error("Service '#{service}' not supported")
  else
    throw new Error("Key '#{k}' not found in LANGUAGE_OBJ")

class TranslatorManager
  ###
  TranslatorManager - 多服务翻译管理器
  支持传统翻译和基于Prompt模板的智能翻译功能

  @param {Object} config - 配置对象，包含各种翻译服务的配置信息
  ###
  constructor: (config) ->
    debug.debug 'TranslatorManager config:', config
    @translators = {}
    @waitingQueue = []
    @promptsModel = PromptsModel
    @i18nHelper = i18nHelper

    # Initialize translators with their max usage limits from config
    if config?.azure?.subscriptionKey
      @translators['azure'] = new AzureTranslator(
        config.azure.subscriptionKey,
        config.azure.endpoint,
        config.azure.region,
        config.azure.maxUsage
      )

    if config?.deepL?.key
      @translators['deepl'] = new DeepLTranslator(
        config.deepL.key,
        config.deepL.endpoint,
        config.deepL.maxUsage
      )

    if config?.deepseek?.key
      @translators['deepseek'] = new DeepSeekTranslator(
        config.deepseek.key,
        config.deepseek.endpoint,
        MODEL_DEEPSEEK,
        DEFAULT_PROMPT,
        config.deepseek.maxUsage
      )

    if config?.openAI?.key
      @translators['openAI'] = new OpenAITranslator(
        config.openAI.key,
        config.openAI.endpoint,
        MODEL_OPENAI,
        DEFAULT_PROMPT,
        config.openAI.orgID,
        config.openAI.projectID,
        config.openAI.maxUsage
      )

    if config?.gemini?.key
      @translators['gemini'] = new GeminiTranslator(
        config.gemini.key,
        MODEL_GEMINI,
        DEFAULT_PROMPT,
        config.gemini.maxUsage
      )

    if config?.claude?.key
      @translators['claude'] = new ClaudeTranslator(
        config.claude.key,
        config.claude.endpoint,
        MODEL_CLAUDE,
        DEFAULT_PROMPT,
        config.claude.maxUsage
      )

    if config?.grok?.key
      debug.debug 'Initializing Grok translator with key:', config.grok.key
      @translators['grok'] = new GrokTranslator(
        config.grok.key,
        config.grok.endpoint,
        MODEL_GROK,
        DEFAULT_PROMPT,
        config.grok.maxUsage
      )

    if config?.rm?.endpoint
      @translators['rm'] = new RMTranslator(
        config.rm.endpoint,
        MODEL_RM,
        DEFAULT_PROMPT,
        config.rm.maxUsage
      )

  ###
  获取可用的翻译服务
  @param {Array} translatorList - 翻译服务列表
  @return {String|null} 可用的服务名称或null
  ###
  getAvailableTranslator: (translatorList) ->
    debug.debug '###getAvailableTranslator', translatorList
    # Check translators in the order of translatorList
    for service in translatorList
      translator = @translators[service]
      if translator?
        debug.debug "###service #{service}: usage=#{translator.usageCount}, " +
          "max=#{translator.maxUsage}, available=#{translator.isAvailable()}"
        if translator.isAvailable()
          return service
    return null

  processWaitingQueue: ->
    debug.debug '###processWaitingQueue',@waitingQueue.length
    if @waitingQueue.length > 0
      # Get the first waiting resolve function and execute it
      resolve = @waitingQueue.shift()
      resolve()

  ###
  检查内容是否应该被过滤（不进行翻译）
  @param {String} content - 要检查的内容
  @return {Boolean} true表示应该过滤，false表示可以翻译
  ###
  _shouldFilterContent: (content) ->
    return true unless content and typeof content is 'string'
    return true if content.trim().length is 0

    # 使用i18n过滤规则
    if @i18nHelper?.i18nRegexArray
      for regexObj in @i18nHelper.i18nRegexArray
        if regexObj.regex.test(content)
          debug.debug "Content filtered by i18n rule: #{regexObj.message}", content
          return true

    return false

  ###
  根据场景和内容特征选择合适的Prompt模板
  @param {String} scenario - 应用场景
  @param {String} content - 翻译内容
  @param {Object} options - 选项参数
  @return {Object|null} 选中的模板对象或null
  ###
  _selectPromptTemplate: (scenario, content, options = {}) ->
    return null unless @promptsModel

    try
      # 根据场景获取活跃模板
      templates = await @promptsModel.findActiveByScenario(scenario, {
        tags: options.tags
        limit: 10
      })

      return null if not templates or templates.length is 0

      # 简单的模板选择逻辑：根据内容长度选择
      contentLength = content?.length or 0

      if contentLength < 50
        # 短文本优先选择UI翻译模板
        uiTemplate = templates.find (t) -> 'ui' in (t.tags or [])
        return uiTemplate if uiTemplate

      # 默认返回第一个模板（版本最高的）
      return templates[0]

    catch error
      debug.error 'Error selecting prompt template:', error
      return null

  ###
  使用自定义Prompt进行翻译
  @param {String} content - 翻译内容
  @param {String} customPrompt - 自定义提示词
  @param {String} fromLangKey - 源语言键
  @param {String} toLangKey - 目标语言键
  @param {Array} translatorList - 翻译服务列表
  @return {Array} [翻译结果, 使用的服务名称]
  ###
  _translateWithCustomPrompt: (content, customPrompt, fromLangKey, toLangKey, translatorList) ->
    attemptTranslation = (index) =>
      if index >= translatorList.length
        throw new Error('Translation failed with all services')

      service = translatorList[index]
      translator = @translators[service]

      if translator?
        try
          fromLang = findLanguageValue(fromLangKey, service)
          toLang = findLanguageValue(toLangKey, service)

          # Check if translator is available
          if not translator.isAvailable()
            debug.debug "Translator #{service} is at max usage, trying next service"
            return attemptTranslation(index + 1)

          debug.debug "Using translator #{service} with custom prompt"

          # 构建完整的翻译请求
          fullPrompt = "#{customPrompt} #{fromLang} to #{toLang}: #{content}"
          result = await translator.translate(fullPrompt, fromLang, toLang)

          # Process waiting queue after translation
          @processWaitingQueue()

          if result and result isnt ''
            return [result, service]

          debug.warn "Empty translation result from #{service}, trying next service"
          return attemptTranslation(index + 1)
        catch error
          # Process waiting queue on error
          @processWaitingQueue()

          if error.name is 'authentication_error'
            debug.error "#{service} Translation API Authentication Error"
            throw error
          else
            debug.error "Error using #{service} translator with custom prompt:", {
              errorName: error.name
              errorMessage: error.message
              service: service
            }
            return attemptTranslation(index + 1)
      else
        debug.error "Translator service not found: #{service}"
        return attemptTranslation(index + 1)

    # Try to get an available translator
    service = @getAvailableTranslator(translatorList)
    if not service?
      # If no translator is available, add to waiting queue
      debug.debug 'No translator available, adding to waiting queue'
      await new Promise((resolve) =>
        @waitingQueue.push resolve
      )
      # After being woken up, try again
      service = @getAvailableTranslator(translatorList)
      if not service?
        throw new Error('No translator available after waiting')

    # Start translation with the available translator
    return await attemptTranslation(translatorList.indexOf(service))

  ###
  传统翻译方法（保持向后兼容）
  @param {String} message - 要翻译的消息
  @param {Array} translatorList - 翻译服务列表
  @param {String} fromLangKey - 源语言键
  @param {String} toLangKey - 目标语言键
  @return {Array} [翻译结果, 使用的服务名称]
  ###
  translate: (message, translatorList=TRANSLATORLIST, fromLangKey='en', toLangKey='zh-cn') ->
    attemptTranslation = (index) =>
      if index >= translatorList.length
        throw new Error('Translation failed with all services')

      service = translatorList[index]
      translator = @translators[service]

      if translator?
        try
          fromLang = findLanguageValue(fromLangKey, service)
          toLang = findLanguageValue(toLangKey, service)

          # Check if translator is available
          if not translator.isAvailable()
            debug.debug "Translator #{service} is at max usage, trying next service"
            return attemptTranslation(index + 1)

          debug.debug "Using translator #{service}"

          result = await translator.translate(message, fromLang, toLang)

          # Process waiting queue after translation
          @processWaitingQueue()

          if result and result isnt ''
            return [result, service]

          debug.warn "Empty translation result from #{service}, trying next service"
          return attemptTranslation(index + 1)
        catch error
          # Process waiting queue on error
          @processWaitingQueue()

          if error.name is 'authentication_error'
            debug.error "#{service} Translation API Authentication Error"
            throw error
          else
            debug.error "Error using #{service} translator:", {
              errorName: error.name
              errorMessage: error.message
              errorDetails: error
              service: service
            }
            return attemptTranslation(index + 1)
      else
        debug.error "Translator service not found: #{service}"
        return attemptTranslation(index + 1)

    # Try to get an available translator
    service = @getAvailableTranslator(translatorList)
    if not service?
      # If no translator is available, add to waiting queue
      debug.debug 'No translator available, adding to waiting queue'
      await new Promise((resolve) =>
        @waitingQueue.push resolve
      )
      # After being woken up, try again
      service = @getAvailableTranslator(translatorList)
      if not service?
        throw new Error('No translator available after waiting')

    # Start translation with the available translator
    return await attemptTranslation(translatorList.indexOf(service))

  ###
  基于场景的智能翻译功能
  根据指定场景自动选择合适的Prompt模板进行翻译

  @param {String} content - 要翻译的内容
  @param {String} scenario - 应用场景 ('ui_translation', 'forum_translation', 'general')
  @param {String} targetLang - 目标语言键 (默认: 'zh-cn')
  @param {Object} options - 选项参数
  @param {String} options.fromLang - 源语言键 (默认: 'en')
  @param {Array} options.translatorList - 翻译服务列表 (默认: TRANSLATORLIST)
  @param {Array} options.tags - 模板标签过滤
  @param {Object} options.variables - 额外的模板变量
  @return {Object} 翻译结果对象 {success: boolean, result?: string, service?: string, error?: string}
  ###
  translateWithPrompt: (content, scenario, targetLang = 'zh-cn', options = {}) ->
    try
      # 参数验证
      unless content and typeof content is 'string'
        return {success: false, error: 'Invalid content parameter'}

      unless scenario and typeof scenario is 'string'
        return {success: false, error: 'Invalid scenario parameter'}

      # 检查内容是否应该被过滤
      if @_shouldFilterContent(content)
        debug.debug 'Content filtered, skipping translation:', content
        return {success: false, error: 'Content filtered by i18n rules', filtered: true}

      # 设置默认选项
      fromLang = options.fromLang or 'en'
      translatorList = options.translatorList or TRANSLATORLIST

      # 选择合适的Prompt模板
      template = await @_selectPromptTemplate(scenario, content, options)

      if not template
        debug.warn "No suitable template found for scenario: #{scenario}"
        # 降级到传统翻译方法
        [result, service] = await @translate(content, translatorList, fromLang, targetLang)
        return {success: true, result: result, service: service, fallback: true}

      debug.debug "Selected template: #{template._id} for scenario: #{scenario}"

      # 准备模板变量
      variables = Object.assign({
        text: content
        target_language: findLanguageValue(targetLang, 'openAI') # 使用通用的语言名称
      }, options.variables or {})

      # 替换模板变量
      promptResult = @promptsModel.replaceVariables(template, variables)
      if not promptResult.success
        debug.error 'Failed to replace template variables:', promptResult.error
        return {success: false, error: promptResult.error}

      # 使用自定义Prompt进行翻译
      [result, service] = await @_translateWithCustomPrompt(
        content,
        promptResult.content.main,
        fromLang,
        targetLang,
        translatorList
      )

      return {
        success: true
        result: result
        service: service
        template: template._id
        scenario: scenario
      }

    catch error
      debug.error 'Error in translateWithPrompt:', error
      return {success: false, error: error.message}

  ###
  评论过滤功能
  使用LLM对评论内容进行智能过滤和审核

  @param {String} content - 要过滤的评论内容
  @param {Object} options - 选项参数
  @param {Array} options.translatorList - 翻译服务列表 (默认: TRANSLATORLIST)
  @param {String} options.language - 内容语言 (默认: 'en')
  @return {Object} 过滤结果对象 {success: boolean, passed: boolean, reason?: string, service?: string}
  ###
  filterComments: (content, options = {}) ->
    try
      # 参数验证
      unless content and typeof content is 'string'
        return {success: false, error: 'Invalid content parameter'}

      # 基本内容检查
      if content.trim().length is 0
        return {success: true, passed: false, reason: 'Empty content'}

      # 设置默认选项
      translatorList = options.translatorList or TRANSLATORLIST
      language = options.language or 'en'

      # 选择评论过滤模板
      template = await @_selectPromptTemplate('comment_filter', content, options)

      if not template
        debug.warn 'No comment filter template found, using default filtering'
        # 简单的默认过滤逻辑
        if content.length > 1000
          return {success: true, passed: false, reason: 'Content too long'}
        return {success: true, passed: true, reason: 'Default filter passed'}

      debug.debug "Using comment filter template: #{template._id}"

      # 准备模板变量
      variables = {
        content: content
        language: language
      }

      # 替换模板变量
      promptResult = @promptsModel.replaceVariables(template, variables)
      if not promptResult.success
        debug.error 'Failed to replace filter template variables:', promptResult.error
        return {success: false, error: promptResult.error}

      # 使用LLM进行内容过滤
      [result, service] = await @_translateWithCustomPrompt(
        content,
        promptResult.content.main,
        language,
        language, # 过滤不需要翻译，保持同一语言
        translatorList
      )

      # 解析过滤结果
      # 假设LLM返回格式为 "PASS" 或 "REJECT: reason"
      passed = false
      reason = 'Unknown filter result'

      if result
        resultLower = result.toLowerCase().trim()
        if resultLower.includes('pass') or resultLower.includes('approved')
          passed = true
          reason = 'Content approved'
        else if resultLower.includes('reject') or resultLower.includes('denied')
          passed = false
          # 尝试提取拒绝原因
          reasonMatch = result.match(/reject[ed]?:?\s*(.+)/i)
          reason = reasonMatch?[1]?.trim() or 'Content rejected'
        else
          # 如果结果不明确，默认拒绝
          passed = false
          reason = 'Unclear filter result'

      return {
        success: true
        passed: passed
        reason: reason
        service: service
        template: template._id
      }

    catch error
      debug.error 'Error in filterComments:', error
      # 过滤失败时默认拒绝
      return {success: false, passed: false, error: error.message}

exports.createTranslatorManager = (config)-> new TranslatorManager(config)
exports.findLanguageValue = findLanguageValue
