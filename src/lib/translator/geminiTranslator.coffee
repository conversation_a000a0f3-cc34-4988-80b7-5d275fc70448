{ GoogleGenerativeAI } = require ("@google/generative-ai")
debugHelper = require '../debug'
debug = debugHelper.getDebugger()
AITranslator = require './AITranslator'


class GeminiTranslator extends AITranslator
  constructor: (apiKey,model,prompt,maxUsage) ->
    super(apiKey,null,model,prompt,maxUsage)
    @genAI = new GoogleGenerativeAI(@apiKey)
    @modelGemini = @genAI.getGenerativeModel({model:@model})

  translate: (message, fromLang = 'English', toLang = 'Chinese') ->
    @_executeTranslation("#{@prompt} #{fromLang} to #{toLang}: #{message}")

  ###
  使用自定义prompt进行翻译
  ###
  translateWithCustomPrompt: (content, customPrompt, fromLang = 'English', toLang = 'Chinese') ->
    @_executeTranslation("#{customPrompt} #{fromLang} to #{toLang}: #{content}")

  ###
  执行翻译请求的通用方法
  ###
  _executeTranslation: (promptMessage) ->
    try
      @use()
      result = await @modelGemini.generateContent(promptMessage)
      response = await result.response
      debug.debug response

      translatedContent = response.text()

      if translatedContent.startsWith('Error')  # Replace this condition as needed
        throw new Error(translatedContent)

      translatedContentDeleteN = translatedContent.replace(/\n+$/, '')
      return translatedContentDeleteN

    catch error
      debug.error 'Gemini Translation API Error:', error.message
      throw error
    finally
      @release()

module.exports = GeminiTranslator